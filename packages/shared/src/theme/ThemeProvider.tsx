/**
 * Theme Provider Component
 *
 * This component provides the theme context to the application.
 */

import React, { useState, createContext, useContext } from 'react';
import { ThemeProvider as StyledThemeProvider, DefaultTheme } from 'styled-components';
import { Theme } from './types';
import { f1Theme } from './f1Theme';
import { lightTheme } from './lightTheme';
import { darkTheme } from './darkTheme';
import GlobalStyles from './GlobalStyles';

// Map of available themes
const themes: Record<string, Theme> = {
  f1: f1Theme,
  light: lightTheme,
  dark: darkTheme,
};

// Default theme
const defaultTheme = f1Theme;

// Helper to get a theme by name
const getTheme = (themeName: string): Theme => {
  return themes[themeName] || defaultTheme;
};

// Create theme context
export const ThemeContext = createContext<{
  theme: Theme;
  setTheme: (theme: Theme | string) => void;
}>({
  theme: defaultTheme,
  setTheme: () => {},
});

// Hook to use the theme
export const useTheme = () => useContext(ThemeContext);

interface ThemeProviderProps {
  /** The initial theme to use */
  initialTheme?: string | Theme;
  /** Whether to store the theme in local storage */
  persistTheme?: boolean;
  /** The key to use for storing the theme in local storage */
  storageKey?: string;
  /** The child components */
  children: React.ReactNode;
}

/**
 * Theme Provider Component
 *
 * Provides theme context to the application and handles theme switching.
 */
export const ThemeProvider = ({
  initialTheme = defaultTheme,
  persistTheme = true,
  storageKey = 'adhd-dashboard-theme',
  children,
}: ThemeProviderProps) => {
  console.log('ThemeProvider rendering with initialTheme:', initialTheme);

  // Initial theme setup
  const [theme, setThemeState] = useState<Theme>(() => {
    console.log('ThemeProvider initializing theme state');

    // Try to load from localStorage
    if (persistTheme && typeof window !== 'undefined') {
      console.log('ThemeProvider attempting to load theme from localStorage');
      const storedTheme = window.localStorage.getItem(storageKey);
      console.log('ThemeProvider storedTheme:', storedTheme);

      if (storedTheme) {
        try {
          // Try to get the theme by name first
          console.log('ThemeProvider trying to get theme by name:', storedTheme);
          const themeByName = getTheme(storedTheme);
          if (themeByName) {
            console.log('ThemeProvider found theme by name:', themeByName.name);
            return themeByName;
          }

          // Otherwise, try to parse as JSON
          console.log('ThemeProvider trying to parse theme as JSON');
          const parsedTheme = JSON.parse(storedTheme) as Theme;
          console.log('ThemeProvider parsed theme:', parsedTheme);
          return parsedTheme;
        } catch (error) {
          console.error('Failed to parse stored theme:', error);
        }
      }
    }

    // Fall back to initial theme
    console.log('ThemeProvider falling back to initial theme');
    const resolvedTheme = typeof initialTheme === 'string' ? getTheme(initialTheme) : initialTheme;
    console.log('ThemeProvider resolved theme:', resolvedTheme);
    return resolvedTheme;
  });

  // Theme change handler
  const setTheme = (newTheme: Theme | string) => {
    const themeObject = typeof newTheme === 'string' ? getTheme(newTheme) : newTheme;
    setThemeState(themeObject);

    // Save to localStorage if enabled
    if (persistTheme && typeof window !== 'undefined') {
      window.localStorage.setItem(storageKey, themeObject.name || JSON.stringify(themeObject));
    }
  };

  // Create a wrapper component to avoid TypeScript issues
  const ThemeWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    console.log('ThemeWrapper rendering with theme:', theme.name);

    // Add visible debugging element
    const debugStyle = {
      position: 'fixed',
      top: '40px',
      left: 0,
      padding: '10px',
      background: theme.colors.primary,
      color: 'white',
      zIndex: 9999,
      fontSize: '16px',
      fontFamily: 'monospace',
    };

    return (
      <StyledThemeProvider theme={theme as DefaultTheme}>
        <GlobalStyles />
        <div style={debugStyle}>Theme: {theme.name}</div>
        {children}
      </StyledThemeProvider>
    );
  };

  // Provide the theme context
  console.log('ThemeProvider returning context with theme:', theme.name);
  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      <ThemeWrapper>{children}</ThemeWrapper>
    </ThemeContext.Provider>
  );
};
