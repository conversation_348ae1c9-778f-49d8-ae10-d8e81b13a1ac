/**
 * Trade Detail Component
 * 
 * Displays detailed information about a selected trade
 */

import React from 'react';
import styled from 'styled-components';
import { Trade } from '../types';
import { useTradeAnalysis } from '../context/TradeAnalysisContext';
import { Badge, Card, Tag } from '@adhd-trading-dashboard/shared';

interface TradeDetailProps {
  className?: string;
}

const Container = styled(Card)`
  margin-top: ${({ theme }) => theme.spacing.md};
`;

const DetailGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;

const DetailSection = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const DetailLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-bottom: ${({ theme }) => theme.spacing.xxs};
`;

const DetailValue = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const ProfitLoss = styled.div<{ value: number }>`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme, value }) => 
    value > 0 ? theme.colors.profit : 
    value < 0 ? theme.colors.loss : 
    theme.colors.textSecondary};
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`;

const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-top: ${({ theme }) => theme.spacing.xs};
`;

const Notes = styled.div`
  margin-top: ${({ theme }) => theme.spacing.md};
  padding-top: ${({ theme }) => theme.spacing.md};
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  white-space: pre-wrap;
`;

const EmptyState = styled.div`
  padding: ${({ theme }) => theme.spacing.lg};
  text-align: center;
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;

export const TradeDetail: React.FC<TradeDetailProps> = ({ className }) => {
  const { data, selectedTradeId } = useTradeAnalysis();
  
  if (!data || !selectedTradeId) {
    return null;
  }
  
  const selectedTrade = data.trades.find(trade => trade.id === selectedTradeId);
  
  if (!selectedTrade) {
    return <EmptyState>Trade not found.</EmptyState>;
  }
  
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };
  
  const formatPercent = (value: number): string => {
    return `${value > 0 ? '+' : ''}${value.toFixed(2)}%`;
  };
  
  const getDirectionVariant = (direction: string): string => {
    return direction === 'long' ? 'success' : 'error';
  };
  
  const getStatusVariant = (status: string): string => {
    switch (status) {
      case 'win': return 'success';
      case 'loss': return 'error';
      case 'breakeven': return 'info';
      default: return 'default';
    }
  };
  
  const calculateDuration = (entryTime: string, exitTime: string): string => {
    const entry = new Date(entryTime).getTime();
    const exit = new Date(exitTime).getTime();
    const durationMs = exit - entry;
    
    const minutes = Math.floor(durationMs / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (hours > 0) {
      return `${hours}h ${remainingMinutes}m`;
    }
    return `${minutes}m`;
  };
  
  return (
    <Container 
      className={className}
      title={`${selectedTrade.symbol} Trade Details`}
      variant="default"
      padding="medium"
    >
      <ProfitLoss value={selectedTrade.profitLoss}>
        {formatCurrency(selectedTrade.profitLoss)} ({formatPercent(selectedTrade.profitLossPercent)})
      </ProfitLoss>
      
      <DetailGrid>
        <DetailSection>
          <DetailLabel>Direction</DetailLabel>
          <DetailValue>
            <Badge 
              variant={getDirectionVariant(selectedTrade.direction) as any}
              size="small"
            >
              {selectedTrade.direction}
            </Badge>
          </DetailValue>
        </DetailSection>
        
        <DetailSection>
          <DetailLabel>Status</DetailLabel>
          <DetailValue>
            <Badge 
              variant={getStatusVariant(selectedTrade.status) as any}
              size="small"
            >
              {selectedTrade.status}
            </Badge>
          </DetailValue>
        </DetailSection>
        
        <DetailSection>
          <DetailLabel>Entry Time</DetailLabel>
          <DetailValue>{formatDate(selectedTrade.entryTime)}</DetailValue>
        </DetailSection>
        
        <DetailSection>
          <DetailLabel>Exit Time</DetailLabel>
          <DetailValue>{formatDate(selectedTrade.exitTime)}</DetailValue>
        </DetailSection>
        
        <DetailSection>
          <DetailLabel>Duration</DetailLabel>
          <DetailValue>{calculateDuration(selectedTrade.entryTime, selectedTrade.exitTime)}</DetailValue>
        </DetailSection>
        
        <DetailSection>
          <DetailLabel>Entry Price</DetailLabel>
          <DetailValue>{selectedTrade.entryPrice.toFixed(2)}</DetailValue>
        </DetailSection>
        
        <DetailSection>
          <DetailLabel>Exit Price</DetailLabel>
          <DetailValue>{selectedTrade.exitPrice.toFixed(2)}</DetailValue>
        </DetailSection>
        
        <DetailSection>
          <DetailLabel>Quantity</DetailLabel>
          <DetailValue>{selectedTrade.quantity}</DetailValue>
        </DetailSection>
        
        <DetailSection>
          <DetailLabel>Timeframe</DetailLabel>
          <DetailValue>{selectedTrade.timeframe}</DetailValue>
        </DetailSection>
        
        <DetailSection>
          <DetailLabel>Session</DetailLabel>
          <DetailValue>{selectedTrade.session}</DetailValue>
        </DetailSection>
        
        <DetailSection>
          <DetailLabel>Strategy</DetailLabel>
          <DetailValue>{selectedTrade.strategy}</DetailValue>
        </DetailSection>
      </DetailGrid>
      
      {selectedTrade.tags && selectedTrade.tags.length > 0 && (
        <DetailSection>
          <DetailLabel>Tags</DetailLabel>
          <TagsContainer>
            {selectedTrade.tags.map((tag, index) => (
              <Tag key={index} variant="info" size="small">
                {tag}
              </Tag>
            ))}
          </TagsContainer>
        </DetailSection>
      )}
      
      {selectedTrade.notes && (
        <Notes>
          <DetailLabel>Notes</DetailLabel>
          <DetailValue>{selectedTrade.notes}</DetailValue>
        </Notes>
      )}
    </Container>
  );
};
