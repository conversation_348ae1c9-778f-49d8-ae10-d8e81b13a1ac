/**
 * Trades Table Component
 * 
 * Displays a table of trades with sorting and filtering
 */

import React, { useState, useMemo } from 'react';
import styled from 'styled-components';
import { Trade, TradeDirection, TradeStatus } from '../types';
import { useTradeAnalysis } from '../context/TradeAnalysisContext';
import { Badge, Tag } from '@adhd-trading-dashboard/shared';

interface TradesTableProps {
  className?: string;
}

type SortField = 'entryTime' | 'symbol' | 'direction' | 'profitLoss' | 'profitLossPercent' | 'status';
type SortDirection = 'asc' | 'desc';

const Container = styled.div`
  overflow-x: auto;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const TableHead = styled.thead`
  background-color: ${({ theme }) => theme.colors.background};
  position: sticky;
  top: 0;
  z-index: 1;
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr<{ isSelected?: boolean }>`
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme, isSelected }) => 
    isSelected ? `${theme.colors.primary}10` : 'transparent'};
  
  &:hover {
    background-color: ${({ theme }) => theme.colors.background};
  }
`;

const TableHeaderCell = styled.th<{ sortable?: boolean; active?: boolean }>`
  padding: ${({ theme }) => theme.spacing.sm};
  text-align: left;
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme, active }) => active ? theme.colors.primary : theme.colors.textPrimary};
  cursor: ${({ sortable }) => sortable ? 'pointer' : 'default'};
  
  &:hover {
    ${({ sortable, theme }) => sortable && `
      color: ${theme.colors.primary};
    `}
  }
`;

const TableCell = styled.td`
  padding: ${({ theme }) => theme.spacing.sm};
  vertical-align: middle;
`;

const SortIcon = styled.span<{ direction: SortDirection }>`
  display: inline-block;
  margin-left: ${({ theme }) => theme.spacing.xs};
  
  &::after {
    content: '${({ direction }) => direction === 'asc' ? '↑' : '↓'}';
  }
`;

const DirectionBadge = styled(Badge)<{ direction: TradeDirection }>`
  text-transform: capitalize;
`;

const StatusBadge = styled(Badge)<{ status: TradeStatus }>`
  text-transform: capitalize;
`;

const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const ProfitLoss = styled.span<{ value: number }>`
  color: ${({ theme, value }) => 
    value > 0 ? theme.colors.profit : 
    value < 0 ? theme.colors.loss : 
    theme.colors.textSecondary};
  font-weight: ${({ theme, value }) => 
    value !== 0 ? theme.fontWeights.medium : theme.fontWeights.regular};
`;

const EmptyState = styled.div`
  padding: ${({ theme }) => theme.spacing.lg};
  text-align: center;
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;

export const TradesTable: React.FC<TradesTableProps> = ({ className }) => {
  const { data, selectedTradeId, selectTrade } = useTradeAnalysis();
  const [sortField, setSortField] = useState<SortField>('entryTime');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default direction
      setSortField(field);
      setSortDirection('desc');
    }
  };
  
  const sortedTrades = useMemo(() => {
    if (!data?.trades) return [];
    
    return [...data.trades].sort((a, b) => {
      let comparison = 0;
      
      switch (sortField) {
        case 'entryTime':
          comparison = new Date(a.entryTime).getTime() - new Date(b.entryTime).getTime();
          break;
        case 'symbol':
          comparison = a.symbol.localeCompare(b.symbol);
          break;
        case 'direction':
          comparison = a.direction.localeCompare(b.direction);
          break;
        case 'profitLoss':
          comparison = a.profitLoss - b.profitLoss;
          break;
        case 'profitLossPercent':
          comparison = a.profitLossPercent - b.profitLossPercent;
          break;
        case 'status':
          comparison = a.status.localeCompare(b.status);
          break;
        default:
          comparison = 0;
      }
      
      return sortDirection === 'asc' ? comparison : -comparison;
    });
  }, [data?.trades, sortField, sortDirection]);
  
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };
  
  const formatPercent = (value: number): string => {
    return `${value > 0 ? '+' : ''}${value.toFixed(2)}%`;
  };
  
  const getDirectionVariant = (direction: TradeDirection): string => {
    return direction === 'long' ? 'success' : 'error';
  };
  
  const getStatusVariant = (status: TradeStatus): string => {
    switch (status) {
      case 'win': return 'success';
      case 'loss': return 'error';
      case 'breakeven': return 'info';
      default: return 'default';
    }
  };
  
  const handleRowClick = (tradeId: string) => {
    selectTrade(tradeId === selectedTradeId ? null : tradeId);
  };
  
  if (!data || !data.trades || data.trades.length === 0) {
    return <EmptyState>No trades found for the selected filters.</EmptyState>;
  }
  
  return (
    <Container className={className}>
      <Table>
        <TableHead>
          <TableRow>
            <TableHeaderCell 
              sortable 
              active={sortField === 'entryTime'} 
              onClick={() => handleSort('entryTime')}
            >
              Date/Time
              {sortField === 'entryTime' && <SortIcon direction={sortDirection} />}
            </TableHeaderCell>
            
            <TableHeaderCell 
              sortable 
              active={sortField === 'symbol'} 
              onClick={() => handleSort('symbol')}
            >
              Symbol
              {sortField === 'symbol' && <SortIcon direction={sortDirection} />}
            </TableHeaderCell>
            
            <TableHeaderCell 
              sortable 
              active={sortField === 'direction'} 
              onClick={() => handleSort('direction')}
            >
              Direction
              {sortField === 'direction' && <SortIcon direction={sortDirection} />}
            </TableHeaderCell>
            
            <TableHeaderCell>
              Entry/Exit
            </TableHeaderCell>
            
            <TableHeaderCell 
              sortable 
              active={sortField === 'profitLoss'} 
              onClick={() => handleSort('profitLoss')}
            >
              P&L
              {sortField === 'profitLoss' && <SortIcon direction={sortDirection} />}
            </TableHeaderCell>
            
            <TableHeaderCell 
              sortable 
              active={sortField === 'profitLossPercent'} 
              onClick={() => handleSort('profitLossPercent')}
            >
              P&L %
              {sortField === 'profitLossPercent' && <SortIcon direction={sortDirection} />}
            </TableHeaderCell>
            
            <TableHeaderCell 
              sortable 
              active={sortField === 'status'} 
              onClick={() => handleSort('status')}
            >
              Status
              {sortField === 'status' && <SortIcon direction={sortDirection} />}
            </TableHeaderCell>
            
            <TableHeaderCell>
              Strategy
            </TableHeaderCell>
            
            <TableHeaderCell>
              Tags
            </TableHeaderCell>
          </TableRow>
        </TableHead>
        
        <TableBody>
          {sortedTrades.map((trade) => (
            <TableRow 
              key={trade.id} 
              isSelected={trade.id === selectedTradeId}
              onClick={() => handleRowClick(trade.id)}
            >
              <TableCell>{formatDate(trade.entryTime)}</TableCell>
              <TableCell>{trade.symbol}</TableCell>
              <TableCell>
                <DirectionBadge 
                  direction={trade.direction}
                  variant={getDirectionVariant(trade.direction) as any}
                  size="small"
                >
                  {trade.direction}
                </DirectionBadge>
              </TableCell>
              <TableCell>
                {trade.entryPrice.toFixed(2)} → {trade.exitPrice.toFixed(2)}
              </TableCell>
              <TableCell>
                <ProfitLoss value={trade.profitLoss}>
                  {formatCurrency(trade.profitLoss)}
                </ProfitLoss>
              </TableCell>
              <TableCell>
                <ProfitLoss value={trade.profitLossPercent}>
                  {formatPercent(trade.profitLossPercent)}
                </ProfitLoss>
              </TableCell>
              <TableCell>
                <StatusBadge 
                  status={trade.status}
                  variant={getStatusVariant(trade.status) as any}
                  size="small"
                >
                  {trade.status}
                </StatusBadge>
              </TableCell>
              <TableCell>{trade.strategy}</TableCell>
              <TableCell>
                <TagsContainer>
                  {trade.tags?.map((tag, index) => (
                    <Tag key={index} size="small" variant="default">
                      {tag}
                    </Tag>
                  ))}
                </TagsContainer>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Container>
  );
};
