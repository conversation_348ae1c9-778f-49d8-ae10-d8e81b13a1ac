/**
 * EquityCurve Component
 *
 * Displays a chart showing equity growth over time.
 */

import React from 'react';
import styled from 'styled-components';
import { CompleteTradeData, PerformanceMetrics } from '@adhd-trading-dashboard/shared/types';

// Local interface for chart data
export interface EquityPoint {
  /** The date of the equity point */
  date: string;
  /** The equity value */
  equity: number;
  /** The baseline value for comparison */
  baseline: number;
}

interface EquityCurveProps {
  data: EquityPoint[];
  isLoading: boolean;
}

const ChartContainer = styled.div`
  width: 100%;
  height: 400px;
  position: relative;
  overflow: hidden;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
`;

const ChartSvg = styled.svg`
  width: 100%;
  height: 100%;
  overflow: visible;
`;

const EquityPath = styled.path`
  fill: none;
  stroke: ${({ theme }) => theme.colors.primary};
  stroke-width: 2;
`;

const BaselinePath = styled.path`
  fill: none;
  stroke: ${({ theme }) => theme.colors.textSecondary};
  stroke-width: 1.5;
  stroke-dasharray: 4;
  opacity: 0.5;
`;

const XAxis = styled.line`
  stroke: ${({ theme }) => theme.colors.border};
  stroke-width: 1;
`;

const YAxis = styled.line`
  stroke: ${({ theme }) => theme.colors.border};
  stroke-width: 1;
`;

const ChartLabel = styled.text`
  font-size: 12px;
  fill: ${({ theme }) => theme.colors.textSecondary};
`;

const LoadingPlaceholder = styled.div`
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.textSecondary};
  background-color: ${({ theme }) => theme.colors.chartGrid};
`;

/**
 * EquityCurve Component
 *
 * Visualizes account equity growth over time
 */
const EquityCurve: React.FC<EquityCurveProps> = ({ data, isLoading }) => {
  if (isLoading) {
    return <LoadingPlaceholder>Loading equity curve data...</LoadingPlaceholder>;
  }

  if (!data || data.length === 0) {
    return <LoadingPlaceholder>No equity data available</LoadingPlaceholder>;
  }

  // Chart dimensions and margins
  const margin = { top: 20, right: 30, bottom: 40, left: 60 };
  const width = 800 - margin.left - margin.right;
  const height = 400 - margin.top - margin.bottom;

  // Find min and max values for scales
  const minValue = Math.min(...data.map((d) => d.equity)) * 0.95;
  const maxValue = Math.max(...data.map((d) => d.equity)) * 1.05;

  // Create path for equity curve
  const createPath = (dataPoints: EquityPoint[], valueKey: 'equity' | 'baseline') => {
    const xStep = width / (dataPoints.length - 1);

    return dataPoints
      .map((d, i) => {
        const x = i * xStep;
        // Normalize the y value to the chart height
        const y = height - (((d[valueKey] as number) - minValue) / (maxValue - minValue)) * height;
        return i === 0 ? `M ${x} ${y}` : `L ${x} ${y}`;
      })
      .join(' ');
  };

  // Create path strings
  const equityPath = createPath(data, 'equity');
  const baselinePath = data[0].baseline ? createPath(data, 'baseline') : '';

  // Generate date labels (show every nth date)
  const dateLabels = () => {
    const step = Math.ceil(data.length / 6); // Show ~6 labels
    return data
      .filter((_, i) => i % step === 0 || i === data.length - 1)
      .map((d, i, filtered) => {
        const x = (width / (filtered.length - 1)) * i;
        return (
          <g key={`date-${i}`} transform={`translate(${x}, ${height + 20})`}>
            <ChartLabel textAnchor="middle">{formatDate(d.date)}</ChartLabel>
          </g>
        );
      });
  };

  // Format date for display
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return `${date.getMonth() + 1}/${date.getDate()}`;
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(value);
  };

  // Generate price labels on y-axis (show 5 values)
  const priceLabels = () => {
    const step = (maxValue - minValue) / 4;
    return Array.from({ length: 5 }, (_, i) => {
      const value = minValue + step * i;
      const y = height - ((value - minValue) / (maxValue - minValue)) * height;
      return (
        <g key={`price-${i}`} transform={`translate(0, ${y})`}>
          <XAxis x1={-5} x2={5} y1={0} y2={0} />
          <ChartLabel x="-10" y="5" textAnchor="end">
            {formatCurrency(value)}
          </ChartLabel>
        </g>
      );
    });
  };

  return (
    <ChartContainer>
      <ChartSvg
        viewBox={`0 0 ${width + margin.left + margin.right} ${height + margin.top + margin.bottom}`}
      >
        <g transform={`translate(${margin.left}, ${margin.top})`}>
          {/* Y-axis */}
          <YAxis x1={0} y1={0} x2={0} y2={height} />
          {priceLabels()}

          {/* X-axis */}
          <XAxis x1={0} y1={height} x2={width} y2={height} />
          {dateLabels()}

          {/* Baseline */}
          {baselinePath && <BaselinePath d={baselinePath} />}

          {/* Equity curve */}
          <EquityPath d={equityPath} />
        </g>
      </ChartSvg>
    </ChartContainer>
  );
};

export default EquityCurve;
