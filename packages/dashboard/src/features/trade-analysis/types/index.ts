/**
 * Trade Analysis Types
 *
 * Type definitions for Trade Analysis feature
 */

/**
 * PerformanceMetrics
 *
 * Type for trading performance metrics
 */
export interface PerformanceMetrics {
  winRate: number;
  profitFactor: number;
  averageWin: number;
  averageLoss: number;
  totalTrades: number;
  netProfit: number;
  expectancy: number;
  maxDrawdown: number;
  sharpeRatio: number;
  successStreak: number;
}

/**
 * EquityPoint
 *
 * Type for equity curve data points
 */
export interface EquityPoint {
  date: string;
  equity: number;
  baseline?: number;
}

/**
 * DistributionBar
 *
 * Type for win/loss distribution chart
 */
export interface DistributionBar {
  range: string;
  count: number;
  isWin: boolean;
}
