/**
 * Dashboard Data Hook
 *
 * Custom hook for fetching and managing dashboard data
 */

import { useState, useCallback } from 'react';
import { CompleteTradeData, PerformanceMetrics } from '@adhd-trading-dashboard/shared/types';

interface Metric {
  title: string;
  value: string;
}

interface ChartDataPoint {
  date: string;
  value: number;
}

export const useDashboardData = () => {
  const [metrics, setMetrics] = useState<Metric[]>([
    { title: 'Win Rate', value: '65%' },
    { title: 'Profit Factor', value: '2.3' },
    { title: 'Net Profit', value: '$12,500' },
    { title: 'Total Trades', value: '120' },
  ]);

  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [recentTrades, setRecentTrades] = useState<CompleteTradeData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock data generation for demo purposes
  const generateChartData = () => {
    const data: ChartDataPoint[] = [];
    const today = new Date();

    for (let i = 30; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);

      data.push({
        date: date.toISOString().split('T')[0],
        value: 10000 + Math.random() * 5000,
      });
    }

    return data;
  };

  // Mock trade data
  const generateTradeData = (): CompleteTradeData[] => {
    const trades: CompleteTradeData[] = [];
    const today = new Date();
    const markets = ['MNQ', 'NQ', 'ES', 'MES'];
    const sessions = ['NY Open', 'Lunch Macro', 'MOC'];
    const modelTypes = ['RD-Cont', 'FVG-RD', 'True-RD'];

    for (let i = 0; i < 5; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const isWin = Math.random() > 0.35;
      const profit = isWin ? Math.random() * 1000 : -Math.random() * 500;

      trades.push({
        trade: {
          id: i + 1,
          date: date.toISOString().split('T')[0],
          market: markets[Math.floor(Math.random() * markets.length)],
          direction: Math.random() > 0.5 ? 'Long' : 'Short',
          session: sessions[Math.floor(Math.random() * sessions.length)],
          model_type: modelTypes[Math.floor(Math.random() * modelTypes.length)],
          entry_price: 15000 + Math.random() * 1000,
          exit_price: 15000 + Math.random() * 1000,
          no_of_contracts: Math.floor(Math.random() * 5) + 1,
          achieved_pl: profit,
          r_multiple: profit / 100,
          win_loss: isWin ? 'Win' : 'Loss',
          pattern_quality_rating: Math.floor(Math.random() * 5) + 1,
          entry_time: '09:30',
          exit_time: '10:15',
          notes: `Mock trade ${i + 1}`,
        },
        fvg_details: null,
        setup: null,
        analysis: null,
      });
    }

    return trades;
  };

  const fetchDashboardData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // In a real app, you would fetch data from an API
      // For now, we'll use mock data with a timeout to simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setChartData(generateChartData());
      setRecentTrades(generateTradeData());

      // Update metrics if needed
      // setMetrics([...])
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    metrics,
    chartData,
    recentTrades,
    isLoading,
    error,
    fetchDashboardData,
  };
};
