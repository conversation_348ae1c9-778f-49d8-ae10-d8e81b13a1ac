/**
 * Recent Trades Panel Component
 *
 * Displays a list of recent trades
 */

import React from 'react';
import styled from 'styled-components';
import { CompleteTradeData, PerformanceMetrics } from '@adhd-trading-dashboard/shared/types';

interface RecentTradesPanelProps {
  trades: CompleteTradeData[];
  isLoading?: boolean;
}

const Container = styled.div`
  width: 100%;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHead = styled.thead`
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const TableRow = styled.tr`
  &:nth-child(even) {
    background-color: ${({ theme }) => theme.colors.background};
  }

  &:hover {
    background-color: ${({ theme }) => theme.colors.hover};
  }
`;

const TableHeader = styled.th`
  text-align: left;
  padding: ${({ theme }) => theme.spacing.sm};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const TableCell = styled.td`
  padding: ${({ theme }) => theme.spacing.sm};
  font-size: ${({ theme }) => theme.fontSizes.md};
`;

const DirectionCell = styled(TableCell)<{ direction: 'Long' | 'Short' }>`
  color: ${({ theme, direction }) =>
    direction === 'Long' ? theme.colors.success : theme.colors.danger};
`;

const ResultCell = styled(TableCell)<{ result: 'Win' | 'Loss' | 'breakeven' }>`
  color: ${({ theme, result }) => {
    switch (result) {
      case 'Win':
        return theme.colors.success;
      case 'Loss':
        return theme.colors.danger;
      default:
        return theme.colors.textSecondary;
    }
  }};
`;

const ProfitCell = styled(TableCell)<{ value: number }>`
  color: ${({ theme, value }) =>
    value > 0
      ? theme.colors.success
      : value < 0
      ? theme.colors.danger
      : theme.colors.textSecondary};
`;

const LoadingIndicator = styled.div`
  padding: ${({ theme }) => theme.spacing.lg};
  text-align: center;
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;

export const RecentTradesPanel: React.FC<RecentTradesPanelProps> = ({
  trades,
  isLoading = false,
}) => {
  if (isLoading) {
    return <LoadingIndicator>Loading recent trades...</LoadingIndicator>;
  }

  if (!trades || trades.length === 0) {
    return <LoadingIndicator>No recent trades found</LoadingIndicator>;
  }

  return (
    <Container>
      <Table>
        <TableHead>
          <TableRow>
            <TableHeader>Date</TableHeader>
            <TableHeader>Symbol</TableHeader>
            <TableHeader>Direction</TableHeader>
            <TableHeader>Result</TableHeader>
            <TableHeader>Profit/Loss</TableHeader>
          </TableRow>
        </TableHead>
        <tbody>
          {trades.map((tradeData) => {
            const { trade } = tradeData;
            const result =
              trade.win_loss === 'Win' ? 'Win' : trade.win_loss === 'Loss' ? 'Loss' : 'breakeven';

            return (
              <TableRow key={trade.id}>
                <TableCell>{trade.date}</TableCell>
                <TableCell>{trade.market || 'MNQ'}</TableCell>
                <DirectionCell direction={trade.direction}>
                  {trade.direction === 'Long' ? '▲ Long' : '▼ Short'}
                </DirectionCell>
                <ResultCell result={result}>
                  {result.charAt(0).toUpperCase() + result.slice(1)}
                </ResultCell>
                <ProfitCell value={trade.achieved_pl || 0}>
                  ${(trade.achieved_pl || 0).toFixed(2)}
                </ProfitCell>
              </TableRow>
            );
          })}
        </tbody>
      </Table>
    </Container>
  );
};
