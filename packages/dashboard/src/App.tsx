import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { ThemeProvider } from '@adhd-trading-dashboard/shared';
import { AppRoutes } from './routes';
import AppErrorBoundary from './components/AppErrorBoundary';

/**
 * Main App component for the ADHD Trading Dashboard
 * Using BrowserRouter for better URL structure
 */
function App() {
  const [renderCount, setRenderCount] = useState(0);

  // Debug logging for component mounting
  useEffect(() => {
    console.log('App component mounted');

    // Log DOM structure to check if elements are being rendered
    console.log('Root element:', document.getElementById('root'));
    console.log('Body children count:', document.body.children.length);

    // Increment render count to track re-renders
    setRenderCount((prev) => prev + 1);

    return () => {
      console.log('App component unmounted');
    };
  }, []);

  // Log each render
  console.log(`App rendering (count: ${renderCount})`);

  // Add visible debugging element
  const debugStyle = {
    position: 'fixed',
    top: 0,
    left: 0,
    padding: '10px',
    background: 'red',
    color: 'white',
    zIndex: 9999,
    fontSize: '16px',
    fontFamily: 'monospace',
  };

  return (
    <AppErrorBoundary>
      <ThemeProvider initialTheme="f1">
        <BrowserRouter>
          <div style={debugStyle}>App Rendered: {renderCount}</div>
          <AppRoutes />
        </BrowserRouter>
      </ThemeProvider>
    </AppErrorBoundary>
  );
}

export default App;
