# Unified Scripts CLI

After restructuring, all scripts are now organized into categories and accessible through a unified interface.

## New Structure

```
scripts/
├── cli.js              # Main CLI interface
├── scripts.config.js   # Central configuration
├── build/              # Build and compilation scripts
├── deploy/             # Deployment scripts
├── analysis/           # Code analysis tools
├── maintenance/        # Cleanup and maintenance
└── utils/              # Shared utilities
```

## Usage

### Using the unified CLI:

```bash
# Build commands
./scripts/cli.js build              # Default build
./scripts/cli.js build --production # Production build
./scripts/cli.js build --watch      # Watch mode

# Deploy commands
./scripts/cli.js deploy             # Deploy to default
./scripts/cli.js deploy production  # Deploy to production
./scripts/cli.js deploy --dry-run   # Test deployment

# Analysis commands
./scripts/cli.js analyze            # Run all analyses
./scripts/cli.js analyze bundle     # Analyze bundle size
./scripts/cli.js analyze deps       # Analyze dependencies

# Maintenance commands
./scripts/cli.js clean              # Clean all
./scripts/cli.js clean dist         # Clean dist only
```

### Direct script access:

Scripts can still be run directly from their category folders:
```bash
node scripts/build/build-utils.js
node scripts/analysis/code-analyzer.js
```

## Migrated Scripts

| Original Script | New Location | Notes |
|-----------------|--------------|-------|
| cli.js | utils/cli.js | Moved |
| scripts.config.js | utils/scripts.config.js | Moved |
| test-script.js | utils/test-script.js | Moved |

## Archived Scripts

Scripts that were obsolete or redundant have been moved to `scripts/.archived/`.
