# Scripts Directory

This directory contains all build, deployment, and maintenance scripts for the project.

## Organization

Scripts are organized into categories for better maintainability:

### 📦 Build Scripts (`/build`)
Build and compilation related scripts.

### 🚀 Deploy Scripts (`/deploy`)
Deployment and publishing scripts.

### 📊 Analysis Scripts (`/analysis`)
Code analysis and quality checking tools.

### 🔧 Maintenance Scripts (`/maintenance`)
Cleanup, fixing, and general maintenance utilities.

### 🛠️ Utilities (`/utils`)
Shared utilities and helper functions.

## Quick Start

Use the unified CLI for common tasks:

```bash
# Run any script through the CLI
./scripts/cli.js [command] [options]

# Get help
./scripts/cli.js --help
```

## Configuration

All scripts share a common configuration file at `scripts.config.js`.

## Development

When adding new scripts:
1. Place them in the appropriate category directory
2. Follow the naming convention: `category-action.js`
3. Export reusable functions for potential merging
4. Update the CLI if adding new commands

---
*Last restructured: 2025-05-23T11:33:07.996Z*
