#!/usr/bin/env node

/**
 * Unified CLI for all project scripts
 * Auto-generated by cleanup-scripts.js
 */

import { program } from 'commander';
import path from 'path';

program
  .name('project-scripts')
  .description('Unified interface for all project scripts')
  .version('1.0.0');

// Build commands
program
  .command('build [target]')
  .description('Build the project')
  .option('-w, --watch', 'Watch mode')
  .option('-p, --production', 'Production build')
  .action(async (target, options) => {
    const buildUtilsModule = await import('./build/build-utils.js');
    await buildUtilsModule.default.build(target, options);
  });

// Deploy commands
program
  .command('deploy [environment]')
  .description('Deploy the project')
  .option('-d, --dry-run', 'Dry run')
  .action(async (environment, options) => {
    const deployUtilsModule = await import('./deploy/deploy-utils.js');
    await deployUtilsModule.default.deploy(environment, options);
  });

// Analysis commands
program
  .command('analyze [type]')
  .description('Run code analysis')
  .option('-o, --output <file>', 'Output file')
  .action(async (type, options) => {
    const analysisUtilsModule = await import('./analysis/analysis-utils.js');
    await analysisUtilsModule.default.analyze(type, options);
  });

// Maintenance commands
program
  .command('clean [target]')
  .description('Clean build artifacts and temporary files')
  .action(async (target) => {
    const maintenanceUtilsModule = await import('./maintenance/maintenance-utils.js');
    await maintenanceUtilsModule.default.clean(target);
  });

program.parse(process.argv);
